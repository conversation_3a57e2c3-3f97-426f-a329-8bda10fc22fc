<ng-template [ngIf]="showSpinner$ | async">
  <app-content-placeholder [linesOnly]="true"></app-content-placeholder>
</ng-template>
<ng-template [ngIf]="!(showSpinner$ | async)">
  <div id="document-table-container" class="h-100 d-flex flex-column">
    <div class="row header-wrap">
      <div class="col row review-panel-toolbar btn-toolbar btn-transparent">
        <div class="btn-group-left col-md-6">
          <div *ngIf="selectedViewType == ReviewViewType.EmailThread">
            <button
              class="btn btn-primary"
              matTooltip="Expand all"
              type="button"
              (click)="ExpandCollapse(true)"
            >
              <fa-icon [icon]="['fas', 'plus-square']"></fa-icon>
            </button>
            <button
              class="btn btn-primary"
              matTooltip="Collapse all"
              type="button"
              (click)="ExpandCollapse(false)"
            >
              <fa-icon [icon]="['fas', 'minus-square']"></fa-icon>
            </button>
            <button
              class="btn btn-primary dropdown-toggle"
              type="button"
              id="dropdownMenuButton"
              data-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
            >
              Actions
            </button>
            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
              <li
                class="dropdown-item"
                (click)="showTags(enumTagActionType.TagAllInclusiveEmails)"
              >
                Tag all inclusive emails
              </li>
              <li
                class="dropdown-item"
                (click)="
                  showTags(enumTagActionType.TagWholeThreadOfSelectedDocuments)
                "
              >
                Tag whole thread of selected documents
              </li>
            </div>
          </div>
        </div>
        <div class="col-md-6 text-right">
          <button
            #helpBtn
            id="helpBtn"
            *ngIf="selectedViewType == ReviewViewType.EmailThread"
            class="btn btn-primary"
            matTooltip="Help"
            type="button"
            (mouseenter)="toggleHelp()"
            (mouseleave)="toggleHelp()"
          >
            <i class="fa fa-question-circle"></i>
          </button>
          <button
            class="btn btn-primary"
            matTooltip="Sort Results"
            type="button"
            *ngIf="selectedViewType !== ReviewViewType.EmailThread"
            (click)="openSortingDialog()"
          >
            <fa-icon [icon]="['fas', 'sort']"></fa-icon>
          </button>
          <button
            class="btn btn-primary"
            matTooltip="Save as CSV"
            type="button"
            (click)="onDownloadCsvClicked()"
          >
            <fa-icon [icon]="['fas', 'file-csv']"></fa-icon>
          </button>
          <button
            class="btn btn-primary"
            matTooltip="Show/Hide Columns"
            type="button"
            (click)="onShowHideColumnsClicked()"
          >
            <fa-icon [icon]="['fas', 'columns']"></fa-icon>
          </button>
        </div>
      </div>
    </div>

    <div class="row d-flex flex-grow-1 tree-wrap">
      <div class="col tree-content">
        <div class="h-100">
          <dx-tree-list
            #dxGrid
            id="document-table"
            class="h-100"
            [noDataText]="'Nothing to show'"
            [dataSource]="tableData"
            [keyExpr]="keyId"
            [parentIdExpr]="parentId"
            [expandedRowKeys]="[12]"
            [allowColumnResizing]="true"
            rootValue="-1"
            columnResizingMode="widget"
            (onRowClick)="onRowClick($event)"
            [showRowLines]="true"
            [showBorders]="true"
            [columnAutoWidth]="true"
            [columnMinWidth]="100"
            (onRowPrepared)="onRowPrepared($event)"
            (onContentReady)="onContentReady($event)"
            (onCellPrepared)="onCellPrepared($event)"
            [autoExpandAll]="autoExpandAllTreeNodes"
            [focusedRowEnabled]="false"
            (onSelectionChanged)="onSelectionChanged($event)"
            (onRowExpanding)="onRowExpanding($event)"
            (onRowCollapsing)="onRowCollapsing($event)"
            (onRowCollapsed)="onRowCollapsed($event)"
            (onRowExpanded)="onRowExpanded($event)"
          >
            <dxo-scrolling
              mode="standard"
              rowRenderingMode="standard"
            ></dxo-scrolling>
            <dxo-selection
              [recursive]="false"
              showCheckBoxesMode="always"
              [allowSelectAll]="true"
              mode="multiple"
            ></dxo-selection>
            <dxo-keyboard-navigation
              [enabled]="false"
            ></dxo-keyboard-navigation>

            <dxi-column
              *ngFor="let col of columnsData"
              dataField="{{ col.field }}"
              caption="{{ col.field }}"
              [allowFiltering]="false"
              [allowSorting]="false"
              [allowReordering]="enableColumnReordering(col.headerName)"
              headerCellTemplate="headerTemplate"
              cellTemplate="cellTemplate"
            >
            </dxi-column>
            <div *dxTemplate="let cell of 'tagRuleConflictTemplate'">
              <a (click)="viewConflictedTagRules(cell?.row?.data?.__FileID)"
                >View Conflicted Rules</a
              >
            </div>

            <div *dxTemplate="let headerInfo of 'headerTemplate'">
              <ng-container [ngSwitch]="headerInfo.column.name">
                <!-- <ng-container *ngSwitchCase="'__emailSubject'">
                                <app-doc-select-header *ngIf="totalDocuments > 0"></app-doc-select-header>
                            </ng-container> -->
                <ng-container *ngSwitchCase="'__emailSubject'">
                  <span>Subject</span>
                </ng-container>
                <ng-container *ngSwitchCase="'__doctype'">
                  <div [matTooltip]="'Document Type'">
                    <span [className]="'icon-vod-doc-type'"></span>
                  </div>
                </ng-container>
                <ng-container *ngSwitchCase="'__tagicon'">
                  <span>Tag Color</span>
                </ng-container>
                <ng-container *ngSwitchCase="'__isReviewed'">
                  <div [matTooltip]="'Is Reviewed'">
                    <span [className]="'fa fa-check'"></span>
                  </div>
                </ng-container>
                <ng-container *ngSwitchCase="'Document Family'">
                  <div [matTooltip]="'Document Family'">
                    <span [className]="'icon-vod-family'"></span>
                  </div>
                </ng-container>
                <ng-container *ngSwitchDefault>
                  <div
                    (click)="onHeaderClick(headerInfo)"
                    [matTooltip]="
                      getInternalFieldName(headerInfo?.column?.name)
                    "
                  >
                    {{ headerInfo.column.name }}
                  </div>
                </ng-container>
              </ng-container>
            </div>

            <div *dxTemplate="let cell of 'cellTemplate'">
              <ng-container [ngSwitch]="cell.column.name">
                <ng-container *ngSwitchCase="'__emailSubject'">
                  <div
                    class="d-inline-flex"
                    *ngIf="cell?.row?.data?.__doctype === 'INCLUSIVE_EMAIL'"
                  >
                    <span
                      [className]="
                        'inclusive-email ' +
                        getEmailIcon(
                          cell?.data?.__emailCategory,
                          cell?.data?.__childCount
                        )
                      "
                      class="inclusive-email"
                      title="Inclusive Email"
                    ></span>
                    <span
                      class="pl-1"
                      [ngClass]="setCssClasses(cell)"
                      [title]="cell.text"
                      >{{ cell.text }}</span
                    >
                  </div>
                  <div
                    class="d-inline-flex"
                    *ngIf="cell?.row?.data?.__doctype === 'GENERATED_EMAIL'"
                  >
                    <span
                      [className]="
                        'generated-email ' +
                        getEmailIcon(
                          cell?.data?.__emailCategory,
                          cell?.data?.__childCount
                        )
                      "
                      class="generated-email"
                      title="Generated Email"
                    ></span>
                    <span
                      class="pl-1"
                      [ngClass]="setCssClasses(cell)"
                      [title]="cell.text"
                      >{{ cell.text }}</span
                    >
                  </div>
                  <div
                    class="d-inline-flex"
                    *ngIf="cell?.row?.data?.__doctype === 'DUMMY_EMAIL'"
                  >
                    <span
                      class="svg-icon-email missing-email"
                      title="Missing Email"
                    ></span>
                    <span
                      class="pl-1"
                      [ngClass]="setCssClasses(cell)"
                      [title]="cell.text"
                      >{{ cell.text }}</span
                    >
                  </div>
                  <div
                    class="d-inline-flex"
                    *ngIf="
                      cell?.row?.data?.__doctype !== 'INCLUSIVE_EMAIL' &&
                      cell?.row?.data?.__doctype !== 'GENERATED_EMAIL' &&
                      cell?.row?.data?.__doctype !== 'DUMMY_EMAIL'
                    "
                  >
                    <span
                      [className]="
                        getEmailIcon(
                          cell?.data?.__emailCategory,
                          cell?.data?.__childCount
                        )
                      "
                      [title]="
                        getDocTypeTooltip(
                          cell?.data?.__emailCategory,
                          cell?.data?.__childCount
                        )
                      "
                    ></span>
                    <span
                      class="pl-1"
                      [ngClass]="setCssClasses(cell)"
                      [title]="cell.text"
                      >{{ cell.text }}</span
                    >
                  </div>
                  <div
                    class="d-inline-flex"
                    *ngIf="cell.text === 'EDOC' || cell.data.__isedoc"
                  >
                    <span [className]="'svg-icon-vod-edoc'"></span
                    ><span
                      class="pl-1"
                      [ngClass]="setCssClasses(cell)"
                      [title]="cell.text"
                      >{{ cell.text }}</span
                    >
                  </div>
                </ng-container>
                <ng-container *ngSwitchCase="'Document Type'">
                  <span
                    *ngIf="cell.text === 'INCLUSIVE_EMAIL'"
                    class="icon-vod-email"
                    title="Inclusive Email"
                  ></span>
                  <span
                    *ngIf="cell.text === 'GENERATED_EMAIL'"
                    class="icon-vod-email"
                    title="Generated Email"
                  ></span>
                  <span
                    *ngIf="
                      cell.data.__isemail &&
                      cell.text != 'INCLUSIVE_EMAIL' &&
                      cell.text != 'GENERATED_EMAIL'
                    "
                    [className]="
                      getEmailIcon(
                        cell?.data?.__emailCategory,
                        cell?.data?.__childCount
                      )
                    "
                    [title]="
                      getDocTypeTooltip(
                        cell?.data?.__emailCategory,
                        cell?.data?.__childCount
                      )
                    "
                  ></span>
                  <span
                    *ngIf="cell.data.__isedoc"
                    [className]="'svg-icon-vod-edoc'"
                    title="Edoc File"
                  ></span>
                </ng-container>

                <ng-container *ngSwitchCase="'__tagicon'">
                  <span *ngFor="let item of tagIconDetails">
                    <div
                      *ngFor="
                        let tagIconColorDetail of item[cell.data?.__FileID]
                      "
                      class="tag-icon"
                      style.background-color="{{ tagIconColorDetail.color }}"
                    >
                      <span class="tag-icon-tooltip">{{
                        tagIconColorDetail.tagName
                      }}</span>
                    </div>
                  </span>
                </ng-container>
                <ng-container *ngSwitchCase="'__isReviewed'">
                  <span
                    *ngIf="cell.text === 'Yes'"
                    [className]="'fa fa-check'"
                  ></span>
                </ng-container>
                <ng-container *ngSwitchCase="'__viewConflictedTagRules'">
                  <a (click)="viewConflictedTagRules(cell?.row?.data?.__FileID)"
                    >View Conflicted Rules</a
                  >
                </ng-container>
                <ng-container *ngSwitchCase="'Document Family'">
                  <span
                    *ngIf="cell.text === 'Yes'"
                    [className]="'icon-vod-family'"
                  ></span>
                </ng-container>
                <ng-container *ngSwitchCase="'Edoc Media Length'">
                  <div>{{ cell.text | milisecondsToDuration }}</div>
                </ng-container>
                <ng-container *ngSwitchDefault>
                  <div>{{ cell.text }}</div>
                </ng-container>
              </ng-container>

              <!-- <div style="color:blue" (click)="onCellClick(cell)">{{ cell.text }}</div> -->
              <!-- <div *ngIf="+cell.text > 0" style="display: flex; justify-content: center;">                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           </div> -->
            </div>
          </dx-tree-list>
          <!-- </perfect-scrollbar> -->
          <!-- </div> -->
          <!-- <ag-grid-angular #agGrid class="ag-theme-balham h-100 cast-shadow" *ngIf="'1'=='2'" [getRowNodeId]="getRowNodeId" (dragStopped)="onColumnMoved($event)" [suppressRowClickSelection]="true"></ag-grid-angular> -->
        </div>
      </div>
      <div class="row w-100 mt-2 mx-0 flex-nowrap">
        <div class="col mb-2">
          <span class="detail-focus">{{
            totalDocuments > 0 ? selectedDocumentsCount : 0
          }}</span>
          selected out of
          <span class="detail-focus">{{ totalDocuments }} documents </span
          ><span
            class="detail-focus"
            *ngIf="
              reviewSourceType === 'reviewSet' &&
              selectedReviewSetBatchInfo?.remainingFiles >= 0
            "
            >({{ selectedReviewSetBatchInfo?.remainingFiles }} left to be
            reviewed)</span
          >
        </div>
        <div class="float-left" *ngIf="totalDocuments > 0">
          <dx-number-box
            #numberBox
            class="number-box"
            [min]="1"
            [showSpinButtons]="false"
            (onEnterKey)="keyEnter($event)"
            [value]="currentPage"
            [max]="maxPageSize"
          ></dx-number-box>
        </div>
        <div
          class="float-left"
          style="padding-right: 10px"
          *ngIf="totalDocuments > 0"
        >
          <pagination
            [disabled]="!searchResultParameter?.totalHitCount"
            [boundaryLinks]="true"
            previousText="&lsaquo;"
            nextText="&rsaquo;"
            firstText="&laquo;"
            lastText="&raquo;"
            [totalItems]="totalThreadCount"
            [maxSize]="5"
            [(ngModel)]="currentPage"
            (pageChanged)="pageChanged($event)"
            [itemsPerPage]="pageSize"
          >
          </pagination>
        </div>
      </div>
    </div>
  </div>
  <dx-popover
    target="#helpBtn"
    position="top"
    [width]="400"
    [(visible)]="showHelp"
  >
    <div *dxTemplate="let data = model; of: 'content'">
      <div class="help-popover-text">
        <div class="row">
          <div class="col-md-1"><span class="svg-icon-email"></span></div>
          <div class="col-md-11">Email Document</div>
        </div>
        <div class="row">
          <div class="col-md-1">
            <span class="svg-icon-email-attach"></span>
          </div>
          <div class="col-md-11">Email with Attachment</div>
        </div>
        <div class="row">
          <div class="col-md-1">
            <span class="svg-icon-email generated-email"></span>
          </div>
          <div class="col-md-11">Generated Email</div>
        </div>
        <div class="row">
          <div class="col-md-1">
            <span class="svg-icon-email missing-email"></span>
          </div>
          <div class="col-md-11">Missing Email</div>
        </div>
        <div class="row">
          <div class="col-md-1">
            <span class="svg-icon-email inclusive-email"></span>
          </div>
          <div class="col-md-11">Inclusive Email</div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <span class="text-success">Text with green are replied emails</span>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <span class="color-blue">Text with blue are forwarded emails</span>
          </div>
        </div>
      </div>
    </div>
  </dx-popover>
</ng-template>

<ng-template #custodianMedia>
  <ng-container
    [ngComponentOutlet]="custodianMediaComp | async"
    [ngComponentOutletInjector]="custodianMediaInjector"
    [ngComponentOutletNgModuleFactory]="custodianMediaModule"
  >
  </ng-container>
</ng-template>

<ng-template #checkInBatchWarning>
  <div
    class=""
    cdkDrag
    cdkDragBoundary=".cdk-global-overlay-wrapper"
    cdkDragRootElement=".cdk-overlay-pane"
  >
    <div class="modal-header d-flex col-12" cdkDragHandle>
      <h4 class="modal-title pull-left">Check-in Batch</h4>
      <button
        type="button"
        class="close pull-right close-confirm"
        [mat-dialog-close]="false"
      >
        <span aria-hidden="true" class="fa fa-times"></span>
      </button>
    </div>
    <div class="modal-body h5 text-center mb-0">
      All the documents in this batch are not reviewed.
    </div>
    <div class="modal-footer text-right">
      <button
        [mat-dialog-close]="false"
        type="button"
        class="btn btn-grey close-confirm"
      >
        OK
      </button>
    </div>
  </div>
</ng-template>

<ng-template #tplDocumentSorting>
  <ng-container
    [ngComponentOutlet]="documentSortingComponent | async"
    [ngComponentOutletInjector]="documentSortingInjector"
    [ngComponentOutletNgModuleFactory]="documentSortingModule"
  >
  </ng-container>
</ng-template>

<ng-template #folderSelectorTemplate>
  <ng-container
    [ngComponentOutlet]="folderSelectorComponent | async"
    [ngComponentOutletInjector]="folderInjector"
    [ngComponentOutletNgModuleFactory]="folderSelectorModule"
  >
  </ng-container>
</ng-template>
<ng-template #documentTagTemplate>
  <ng-container
    [ngComponentOutlet]="documentTagComponent | async"
    [ngComponentOutletInjector]="tagInjector"
  >
  </ng-container>
</ng-template>

<ng-template #calBatchWarningMessage>
  <div
    class="row mx-0"
    cdkDrag
    cdkDragBoundary=".cdk-global-overlay-wrapper"
    cdkDragRootElement=".cdk-overlay-pane"
  >
    <div class="modal-header d-flex col-12" cdkDragHandle>
      <h4 class="modal-title pull-left">CAL Reviewset Threshold</h4>
      <button
        type="button"
        class="close pull-right close-confirm"
        (click)="calBatchRef.close(false)"
      >
        <span aria-hidden="true" class="fa fa-times"></span>
      </button>
    </div>
    <div class="modal-body text-center h6 mb-0">
      {{ errMessage }}
    </div>
    <div class="modal-footer w-100 text-right">
      <button
        *ngIf="
          selectedReviewSetInfo?.calProfileInfo?.allowReviewAfterCALThreshold
        "
        type="button"
        class="btn btn-venio-primary float-right close-confirm"
        (click)="calBatchRef.close(true)"
      >
        Continue to Review
      </button>
      <button
        (click)="calBatchRef.close(false)"
        type="button"
        class="btn btn-grey close-confirm"
      >
        Cancel
      </button>
    </div>
  </div>
</ng-template>

<ng-template #confirmationDialog>
  <div
    class=""
    cdkDrag
    cdkDragBoundary=".cdk-global-overlay-wrapper"
    cdkDragRootElement=".cdk-overlay-pane"
  >
    <div class="modal-header d-flex col-12" cdkDragHandle>
      <h4 class="modal-title pull-left">Export to file</h4>
      <button
        type="button"
        class="close pull-right close-confirm"
        [mat-dialog-close]="false"
      >
        <span aria-hidden="true" class="fa fa-times"></span>
      </button>
    </div>
    <div
      class="modal-body h5 text-center mb-0"
      [innerHTML]="confirmationMessageHtml"
    ></div>
    <div class="modal-footer text-right">
      <button
        type="button"
        class="btn btn-primary float-right close-confirm"
        [mat-dialog-close]="true"
      >
        YES
      </button>
      <button
        [mat-dialog-close]="false"
        type="button"
        class="btn btn-grey close-confirm"
      >
        NO
      </button>
    </div>
  </div>
</ng-template>
