import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation
} from '@angular/core'
import { FormControl } from '@angular/forms'
import { Store } from '@ngxs/store'
import {
  SetFolderAction,
  SetFolderScopeAction,
  SetFolderScopeQueryAction,
  SetIsSearchTriggeredFromHistoryAction
} from '../../../../review2/store/review.actions'

@Component({
  selector: 'fbi-caselaunchpad',
  templateUrl: './case-launchpad-fbi.component.html',
  styleUrls: ['./case-launchpad-fbi.scss'],
  encapsulation: ViewEncapsulation.None
})
export class CaseLaunchpadFbiComponent {
  @Input() caseList = []

  @Output() navigateRoute = new EventEmitter()

  @Output() changePage = new EventEmitter()

  page = new FormControl('')
  constructor(private store: Store) {}

  navigateRouter(c, page) {
    /*Reset review2 state.
     * Otherwise folder scope query gets appended in search expression even when changing the project
     */
    this.store.dispatch([
      new SetFolderScopeAction('allDocuments'),
      new SetFolderAction(''),
      new SetFolderScopeQueryAction(''),
      new SetIsSearchTriggeredFromHistoryAction(false)
    ])

    const caseModel = this.caseList.find((ca) => ca.ProjectId === c)
    if (page === 'review') {
      this.navigateRoute.emit({ module: 'review', project: caseModel })
    } else if (page === 'analyze') {
      this.navigateRoute.emit({ module: 'analyze', project: caseModel })
    } else if (page === 'production') {
      this.navigateRoute.emit({ module: 'production', project: caseModel })
    } else if (page === 'add') {
      this.navigateRoute.emit({ module: 'add', project: caseModel })
    }
  }

  handlePagination(page) {
    this.changePage.emit({ page: page })
  }
}
