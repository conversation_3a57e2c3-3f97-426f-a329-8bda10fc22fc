import { Component, OnInit, ViewChild } from '@angular/core'
import { Form<PERSON>uilder, FormGroup } from '@angular/forms'
import { MatDialog } from '@angular/material/dialog'
import { ActivatedRoute } from '@angular/router'
import { ConfigService } from '@config/services/config.service'
import { Action, Store as rxStore } from '@ngrx/store'
import { Store } from '@ngxs/store'
import { ConfirmationDialogComponent } from '@root/modules/launchpad/components/confirmation-dialog/confirmation-dialog.component'
import {
  DIITokenModel,
  EncodingType,
  ProductionFormat,
  ProductionFormatModel,
  ProductionFormatType,
  TiffLoadFileFormat
} from '@root/modules/production2/models/production-format.model'
import { Production2FormService } from '@root/modules/production2/services/production-form.service'
import { ProductionSettingsService } from '@root/modules/production2/services/production-settings.service'
import { GetProductionFormatsAction } from '@root/modules/production2/store/production-format/production-format.actions'
import { ProductionFormatStateSelector } from '@root/modules/production2/store/production-format/production-format.selectors'
import { GlobalErrorAction } from '@root/store/actions'
import { ResponseModel } from '@shared/models/response.model'
import { DxDataGridComponent } from 'devextreme-angular'
import { cloneDeep } from 'lodash'
import { BsModalService } from 'ngx-bootstrap/modal'
import { EMPTY, Observable, Subject } from 'rxjs'
import { catchError, filter, switchMap, takeUntil } from 'rxjs/operators'

@Component({
  selector: 'app-production-format',
  templateUrl: './production-format.component.html',
  styleUrls: ['./production-format.component.scss']
})
export class ProductionFormatComponent implements OnInit {
  config = ConfigService

  templateProductionFormat: ProductionFormat

  productionFormats: ProductionFormatModel[] = []

  diiTokens: DIITokenModel[] = []

  preservedDIITokens: DIITokenModel[] = []

  selectedDIITokenKeys: number[] = []

  selectedProductionFormatKeys: number[] = []

  advancedDelimiterOptionAvailable = false

  isDIIPopoverOpen = false

  isTokenInfoTooltipVisible = false

  tokenIfo = ''

  selectedTiffLoadFileFormats: TiffLoadFileFormat[] = []

  fieldSeparatorDelimiters: string[] = []

  textQualifierDelimiters: string[] = []

  lineBreakDelimiters: string[] = []

  encodingTypes: any[] = [
    { id: 1, name: 'UTF8', type: EncodingType.UTF8 },
    { id: 2, name: 'UTF16', type: EncodingType.UTF16 },
    { id: 3, name: 'ANSI', type: EncodingType.ANSI }
  ]

  @ViewChild('productionFormatDataGridRef', { static: false })
  productionFormatDataGrid: DxDataGridComponent

  @ViewChild('diiTokensDataGridRef', { static: false })
  diiTokensDataGrid: DxDataGridComponent

  private unsubscribe$ = new Subject<void>()

  isOpticonChecked = false

  isLfpChecked = false

  isDiiChecked = false

  showLoadFormatTooltipInfo = false

  showCrossReferenceTooltipInfo = false

  isDiiTokensTooltipVisible = false

  isResetDIITokensInfoTooltipVisible = false

  projectId: number

  reproduceOriginalExportHasTiff = false

  constructor(
    public formService: Production2FormService,
    private dialog: MatDialog,
    private store: Store,
    private rxStore: rxStore<Action>,
    private fb: FormBuilder,
    private configService: ConfigService,
    private modalService: BsModalService,
    private service: ProductionSettingsService,
    private route: ActivatedRoute
  ) {
    this.projectId = +this.route.snapshot.queryParams['projectId']
  }

  get productionFormatFormGroup(): FormGroup {
    return (this.formService.formGroup as FormGroup).controls
      .productionFormat as FormGroup
  }

  get folderStructureFormGroup(): FormGroup {
    return (this.formService.formGroup as FormGroup).controls
      .folderStructure as FormGroup
  }

  get folderStructureAdvancedOptionsFormGroup(): FormGroup {
    return (
      (this.formService.formGroup as FormGroup).controls
        .folderStructure as FormGroup
    ).controls.advancedOptions as FormGroup
  }

  ngOnInit(): void {
    this.formService.currentProductionDestinationDependencyState.subscribe(
      (response) => {
        if (response) {
          if (
            this.productionFormatFormGroup?.controls?.productionFormats ===
              null ||
            this.productionFormatFormGroup?.controls?.productionFormats
              ?.value === null ||
            this.productionFormatFormGroup?.controls?.productionFormats?.value
              .length === 0
          ) {
            if (
              this.templateProductionFormat.productionFormats != null &&
              this.templateProductionFormat.productionFormats.length > 0
            ) {
              const formatTypes =
                this.templateProductionFormat.productionFormats.map(
                  (x) => x.formatType
                )
              this.selectedProductionFormatKeys = this.productionFormats
                .filter((x) => formatTypes.includes(x.formatType))
                .map((x) => x.formatId)
            } else {
              this.selectedProductionFormatKeys = [2]
            }
          }
        }
      }
    )
    this.formService.currentProductionFormat.subscribe((response) => {
      if (response) {
        this.templateProductionFormat = response

        this.isOpticonChecked = this.isLoadFileFormatChecked(
          TiffLoadFileFormat.OPTICON
        )
        this.isLfpChecked = this.isLoadFileFormatChecked(TiffLoadFileFormat.LFP)
        this.isDiiChecked = this.isLoadFileFormatChecked(TiffLoadFileFormat.DII)

        this.store
          .dispatch(new GetProductionFormatsAction())
          .pipe(
            switchMap(() =>
              this.store.select(
                ProductionFormatStateSelector.SliceOf('productionFormats')
              )
            ),
            filter((response) => !!response),
            takeUntil(this.unsubscribe$)
          )
          .subscribe((response: ProductionFormatModel[]) => {
            if (response != null && response.length > 0) {
              /** Clone all the available production formats in temp variable */
              const tempProductionFormats = cloneDeep(response)

              /** Update values such as encoding, delimiters info, include text etc into temp vairable from template production formats  */
              if (this.templateProductionFormat.productionFormats) {
                tempProductionFormats.forEach((x) => {
                  this.templateProductionFormat.productionFormats.forEach(
                    (y) => {
                      if (x.formatType === y.formatType) {
                        x.encodingType = y.encodingType
                        x.includeText = y.includeText
                        x.delimiterInfo = y.delimiterInfo
                      }
                    }
                  )
                })
              }

              /**Set final production formats as datasource for grid */
              this.productionFormats = tempProductionFormats

              /** Set selected row keys */
              if (this.templateProductionFormat.productionFormats) {
                const selectedProductionFormatTypes =
                  this.templateProductionFormat.productionFormats.map(
                    (x) => x.formatType
                  )
                this.advancedDelimiterOptionAvailable =
                  selectedProductionFormatTypes.includes(
                    ProductionFormatType.CSV
                  ) ||
                  selectedProductionFormatTypes.includes(
                    ProductionFormatType.DAT
                  ) ||
                  selectedProductionFormatTypes.includes(
                    ProductionFormatType.CUSTOM
                  )

                const selectedProductionFormats = this.productionFormats.filter(
                  (x) => selectedProductionFormatTypes.includes(x.formatType)
                )
                this.productionFormatFormGroup.controls.productionFormats.patchValue(
                  selectedProductionFormats
                )

                this.selectedProductionFormatKeys =
                  selectedProductionFormats.map((y) => y.formatId)
              } else {
                if (
                  !this.formService.connectorFormGroup?.controls
                    ?.allowRelativityProduction?.value
                ) {
                  this.selectedProductionFormatKeys = []
                }
              }

              /** Set delimiters for CUSTOM format */
              this.updateCustomFormatDelimiters()
            }
          })

        this.checkReproduceOriginalExportHasTiff()
      }
    })

    this.formService.currentFolderStructureOption.subscribe((response) => {
      if (response) {
        if (response.type === 'FULLTEXT' && !response.checked) {
          this.productionFormatFormGroup.controls.crossReferenceFulltext.patchValue(
            false
          )

          const tempProductionFormats = cloneDeep(
            this.productionFormatFormGroup.controls.productionFormats.value
          )
          tempProductionFormats.forEach((element) => {
            element.includeText = false
          })

          this.productionFormatFormGroup.controls.productionFormats.patchValue(
            tempProductionFormats
          )

          const tempProductionFormatsAll = cloneDeep(this.productionFormats)
          tempProductionFormatsAll.forEach((element) => {
            element.includeText = false
          })

          this.productionFormats = tempProductionFormatsAll
        } else if (response.type === 'NATIVE' && !response.checked) {
          if (this.selectedProductionFormatKeys.includes(7)) {
            const confirmMessage =
              '<strong>MDB</strong> format requires to produce native files. Do you want to produce native files?'
            this.showConfirmationModal(confirmMessage)
              .pipe(
                filter((yes) => yes),
                takeUntil(this.unsubscribe$)
              )
              .subscribe(() => {
                this.folderStructureFormGroup.controls.nativeFiles.patchValue(
                  true
                )
              })
          }
          this.productionFormatFormGroup.controls.crossReferenceNative.patchValue(
            false
          )
        } else if (response.type === 'IMAGE' && !response.checked) {
          this.productionFormatFormGroup.controls.crossReferenceNative.patchValue(
            false
          )

          this.isOpticonChecked = false
          this.isLfpChecked = false
          this.isDiiChecked = false
          this.productionFormatFormGroup.controls.tiffFileFormats.patchValue([])
        }
      }
    })
  }

  checkReproduceOriginalExportHasTiff(): void {
    this.reproduceOriginalExportHasTiff = false
    if (this.formService.formGroup?.controls?.productionId?.value > 0) {
      // check OriginalExportHasTiff
      this.service
        .getOriginalExportHasTiff<ResponseModel>(
          this.projectId,
          this.formService.formGroup?.controls?.productionId?.value
        )
        .pipe(
          takeUntil(this.unsubscribe$),
          // catch error here
          catchError((res) => {
            // this.toast.error(res?.error.message)
            return EMPTY
          })
        )
        .subscribe((res: ResponseModel) => {
          this.reproduceOriginalExportHasTiff = res?.data ? true : false
        })
    }
  }

  toggleLoadFormatTooltip(): void {
    this.showLoadFormatTooltipInfo = !this.showLoadFormatTooltipInfo
  }

  toggleCrossReferenceTooltip(): void {
    this.showCrossReferenceTooltipInfo = !this.showCrossReferenceTooltipInfo
  }

  updateCustomFormatDelimiters(): void {
    const customFormat = this.productionFormats.find(
      (x) => x.formatType === ProductionFormatType.CUSTOM
    )

    this.fieldSeparatorDelimiters = cloneDeep(
      customFormat.availableDelimiters
    ).filter(
      (delimiter) =>
        delimiter !== customFormat.delimiterInfo.textQualifier &&
        delimiter !== customFormat.delimiterInfo.lineBreak
    )

    this.textQualifierDelimiters = cloneDeep(
      customFormat.availableDelimiters
    ).filter(
      (delimiter) =>
        delimiter !== customFormat.delimiterInfo.fieldSeparator &&
        delimiter !== customFormat.delimiterInfo.lineBreak
    )

    this.lineBreakDelimiters = cloneDeep(
      customFormat.availableDelimiters
    ).filter(
      (delimiter) =>
        delimiter !== customFormat.delimiterInfo.textQualifier &&
        delimiter !== customFormat.delimiterInfo.fieldSeparator
    )
  }

  onProductionFormatSelectionChanged(e): void {
    if (
      this.formService.connectorFormGroup?.controls?.allowRelativityProduction
        ?.value &&
      e.selectedRowKeys?.length === 0
    ) {
      if (this.formService.isProductionFormatTabActivated) {
        const errorMessage =
          'At least one load format must be selected when relativity connector is enabled.'
        this.rxStore.dispatch(
          new GlobalErrorAction(new Error(errorMessage), true, false)
        )
      }
      this.selectedProductionFormatKeys = e.currentDeselectedRowKeys
      return
    }

    this.selectedProductionFormatKeys = e.selectedRowKeys

    let selectedProductionFormats = []
    if (this.productionFormatFormGroup.controls.productionFormats?.value) {
      selectedProductionFormats = cloneDeep(
        this.productionFormatFormGroup.controls.productionFormats.value
      )
    }

    if (e.currentSelectedRowKeys) {
      e.currentSelectedRowKeys.forEach((formatId) => {
        const currentSelectedProductionFormatInfo = this.productionFormats.find(
          (x) => x.formatId === formatId
        )
        const index: number = selectedProductionFormats.findIndex(
          (x) => x.formatId === formatId
        )
        if (index < 0) {
          selectedProductionFormats.push(currentSelectedProductionFormatInfo)
        }
      })
    }
    if (e.currentDeselectedRowKeys) {
      e.currentDeselectedRowKeys.forEach((formatId) => {
        const index: number = selectedProductionFormats.findIndex(
          (x) => x.formatId === formatId
        )
        if (index > -1) {
          selectedProductionFormats.splice(index, 1)
        }
      })
    }

    this.productionFormatFormGroup.controls.productionFormats.patchValue(
      selectedProductionFormats
    )

    if (
      e.currentSelectedRowKeys.includes(7) &&
      !this.folderStructureFormGroup.controls.nativeFiles?.value
    ) {
      const confirmMessage =
        '<strong>MDB</strong> format requires to produce native files. Do you want to produce native files?'
      this.showConfirmationModal(confirmMessage)
        .pipe(
          filter((yes) => yes),
          takeUntil(this.unsubscribe$)
        )
        .subscribe(() => {
          this.folderStructureFormGroup.controls.nativeFiles.patchValue(true)
        })
    }

    this.formService.updateProductionFormatDependencyState(
      <ProductionFormat>this.productionFormatFormGroup.value
    )
  }

  showConfirmationModal(message: string): Observable<boolean> {
    const modal = this.modalService.show(ConfirmationDialogComponent, {
      class: 'modal-dialog-centered'
    })
    ;(<ConfirmationDialogComponent>modal.content).showConfirmationModal(
      this.configService.companyName,
      message
    )
    return (<ConfirmationDialogComponent>modal.content).onClose
  }

  onEncodingChanged(
    encodingType: EncodingType,
    formatType: ProductionFormatType
  ): void {
    const tempProductionFormats = cloneDeep(this.productionFormats)
    tempProductionFormats.forEach((x) => {
      if (x.formatType === formatType) x.encodingType = encodingType
    })

    const selectedProductionFormats = tempProductionFormats.filter((x) =>
      this.selectedProductionFormatKeys.includes(x.formatId)
    )

    this.productionFormats = tempProductionFormats
    this.productionFormatFormGroup.controls.productionFormats.patchValue(
      selectedProductionFormats
    )
  }

  onIncludeTextChanged(
    includeText: boolean,
    formatType: ProductionFormatType
  ): void {
    const tempProductionFormats = cloneDeep(this.productionFormats)
    tempProductionFormats.forEach((x) => {
      if (x.formatType === formatType) x.includeText = includeText
    })
    this.productionFormats = tempProductionFormats

    const selectedProductionFormats = tempProductionFormats.filter((x) =>
      this.selectedProductionFormatKeys.includes(x.formatId)
    )

    this.productionFormatFormGroup.controls.productionFormats.patchValue(
      selectedProductionFormats
    )
  }

  onCustomFormatFieldSeparatorChanged(
    e: any,
    formatType: ProductionFormatType
  ): void {
    let fieldSeparator = e.value
    if (fieldSeparator === null) {
      fieldSeparator = e.previousValue
    }

    const tempProductionFormats = cloneDeep(this.productionFormats)
    tempProductionFormats.forEach((x) => {
      if (x.formatType === formatType)
        x.delimiterInfo.fieldSeparator = fieldSeparator
    })
    this.productionFormats = tempProductionFormats

    /** Set delimiters for CUSTOM format */
    this.updateCustomFormatDelimiters()

    const selectedProductionFormats = tempProductionFormats.filter((x) =>
      this.selectedProductionFormatKeys.includes(x.formatId)
    )

    this.productionFormatFormGroup.controls.productionFormats.patchValue(
      selectedProductionFormats
    )
  }

  onCustomFormatTextQualifierChanged(
    e: any,
    formatType: ProductionFormatType
  ): void {
    let textQualifier = e.value
    if (textQualifier === null) {
      textQualifier = e.previousValue
    }
    const tempProductionFormats = cloneDeep(this.productionFormats)
    tempProductionFormats.forEach((x) => {
      if (x.formatType === formatType)
        x.delimiterInfo.textQualifier = textQualifier
    })
    this.productionFormats = tempProductionFormats

    /** Set delimiters for CUSTOM format */
    this.updateCustomFormatDelimiters()

    const selectedProductionFormats = tempProductionFormats.filter((x) =>
      this.selectedProductionFormatKeys.includes(x.formatId)
    )

    this.productionFormatFormGroup.controls.productionFormats.patchValue(
      selectedProductionFormats
    )
  }

  onCustomFormatLineBreakChanged(
    e: any,
    formatType: ProductionFormatType
  ): void {
    let lineBreak = e.value
    if (lineBreak === null) {
      lineBreak = e.previousValue
    }

    const tempProductionFormats = cloneDeep(this.productionFormats)
    tempProductionFormats.forEach((x) => {
      if (x.formatType === formatType) x.delimiterInfo.lineBreak = lineBreak
    })
    this.productionFormats = tempProductionFormats

    /** Set delimiters for CUSTOM format */
    this.updateCustomFormatDelimiters()

    const selectedProductionFormats = tempProductionFormats.filter((x) =>
      this.selectedProductionFormatKeys.includes(x.formatId)
    )

    this.productionFormatFormGroup.controls.productionFormats.patchValue(
      selectedProductionFormats
    )
  }

  onOpticonLoadFormatChanged(e: any): void {
    this.isOpticonChecked = e.target.checked
    this.updateTiffLoadFileFormats(TiffLoadFileFormat.OPTICON, e.target.checked)
  }

  onLFPLoadFormatChanged(e: any): void {
    this.isLfpChecked = e.target.checked
    this.updateTiffLoadFileFormats(TiffLoadFileFormat.LFP, e.target.checked)
  }

  onDIILoadFormatChanged(e: any): void {
    this.isDiiChecked = e.target.checked
    this.updateTiffLoadFileFormats(TiffLoadFileFormat.DII, e.target.checked)
  }

  updateTiffLoadFileFormats(
    loadFileFormat: TiffLoadFileFormat,
    checked: boolean
  ): void {
    if (checked && !this.selectedTiffLoadFileFormats.includes(loadFileFormat))
      this.selectedTiffLoadFileFormats.push(loadFileFormat)
    else if (
      !checked &&
      this.selectedTiffLoadFileFormats.includes(loadFileFormat)
    ) {
      const index: number = this.selectedTiffLoadFileFormats.indexOf(
        loadFileFormat,
        0
      )
      if (index > -1) {
        this.selectedTiffLoadFileFormats.splice(index, 1)
      }
    }

    this.productionFormatFormGroup.controls.tiffFileFormats.patchValue(
      this.selectedTiffLoadFileFormats
    )
  }

  isLoadFileFormatChecked(loadFileFormat: TiffLoadFileFormat): boolean {
    const templateTiffLoadFileFormats =
      this.productionFormatFormGroup.controls.tiffFileFormats?.value

    if (templateTiffLoadFileFormats) {
      if (templateTiffLoadFileFormats.includes(loadFileFormat)) {
        if (!this.selectedTiffLoadFileFormats?.includes(loadFileFormat)) {
          this.selectedTiffLoadFileFormats.push(loadFileFormat)
        }
        return true
      }
    }
    return false
  }

  onSelectDIITokenClick(): void {
    this.isDIIPopoverOpen = !this.isDIIPopoverOpen
    if (this.isDIIPopoverOpen) {
      this.isDiiTokensTooltipVisible = false
    }

    const produceFulltext =
      this.folderStructureFormGroup.controls.fullTextFiles.value
    const produceNative =
      this.folderStructureFormGroup.controls.nativeFiles.value
    const produceImage =
      this.folderStructureFormGroup.controls.exportImage.value

    const defaultDIITokens: DIITokenModel[] = [
      {
        tokenId: 1,
        isSelected: true,
        venioFieldName: 'BEG_BATES',
        displayName: '',
        tokenName: 'T',
        isTokenDisplayNameEditable: false,
        isMandatory: true
      },
      {
        tokenId: 2,
        isSelected: false,
        venioFieldName: 'EXPORT_NATIVE_PATH',
        displayName: '',
        tokenName: 'EDOC',
        isTokenDisplayNameEditable: false,
        isMandatory: false
      },
      {
        tokenId: 3,
        isSelected: false,
        venioFieldName: 'EXPORT_NATIVE_PATH',
        displayName: '',
        tokenName: 'EATTACH',
        isTokenDisplayNameEditable: false,
        isMandatory: false
      },
      {
        tokenId: 4,
        isSelected: false,
        venioFieldName: 'BEGIN_FAMILY_BATES_ALL',
        displayName: '',
        tokenName: 'PARENTID',
        isTokenDisplayNameEditable: false,
        isMandatory: false
      },
      {
        tokenId: 5,
        isSelected: false,
        venioFieldName: 'END_BATES',
        displayName: 'ENDDOC#',
        tokenName: 'C',
        isTokenDisplayNameEditable: true,
        isMandatory: false
      },
      {
        tokenId: 6,
        isSelected: false,
        venioFieldName: 'PAGECOUNT',
        displayName: 'PGCount',
        tokenName: 'C',
        isTokenDisplayNameEditable: true,
        isMandatory: false
      },
      {
        tokenId: 7,
        isSelected: false,
        venioFieldName: 'VOLUME',
        displayName: 'VOLUME',
        tokenName: 'C',
        isTokenDisplayNameEditable: true,
        isMandatory: false
      },
      {
        tokenId: 8,
        isSelected: true,
        venioFieldName: 'EXPORT_FULLTEXT_PATH',
        displayName: '',
        tokenName: 'O',
        isTokenDisplayNameEditable: false,
        isMandatory: true
      },
      {
        tokenId: 9,
        isSelected: true,
        venioFieldName: 'DEFAULT_DIRECTORY',
        displayName: '',
        tokenName: 'D',
        isTokenDisplayNameEditable: false,
        isMandatory: true
      }
    ]

    let overiddenDIITokens: DIITokenModel[] = cloneDeep(defaultDIITokens)

    if (this.productionFormatFormGroup.controls.diiTokens.value.length > 0) {
      const templateDIITokens =
        this.productionFormatFormGroup.controls.diiTokens.value

      if (templateDIITokens) {
        overiddenDIITokens.forEach((overiddenToken) => {
          templateDIITokens.forEach((templateToken) => {
            if (
              templateToken.venioFieldName === overiddenToken.venioFieldName &&
              templateToken.tokenName === overiddenToken.tokenName
            ) {
              overiddenToken.isSelected = templateToken.isSelected
            }
            if (
              templateToken.venioFieldName === overiddenToken.venioFieldName &&
              templateToken.tokenName === overiddenToken.tokenName &&
              templateToken.tokenName === 'C'
            ) {
              overiddenToken.displayName = templateToken.displayName
            }
          })
        })
      }
    }

    if (!produceFulltext) {
      overiddenDIITokens = overiddenDIITokens.filter(
        (dii) => dii.tokenName !== 'O'
      )
    }
    if (!produceNative) {
      overiddenDIITokens = overiddenDIITokens.filter(
        (dii) => dii.tokenName !== 'EDOC' && dii.tokenName !== 'EATTACH'
      )
    }

    this.selectedDIITokenKeys = overiddenDIITokens
      .filter((x) => x.isSelected === true)
      .map((y) => y.tokenId)

    this.tokenIfo = this.buildDiiTokenInfo(defaultDIITokens)
    this.diiTokens = overiddenDIITokens

    if (this.preservedDIITokens.length === 0) {
      this.preservedDIITokens = [...overiddenDIITokens]
    }
    this.productionFormatFormGroup.controls.diiTokens.patchValue(
      overiddenDIITokens
    )
  }

  buildDiiTokenInfo(diiTokens: DIITokenModel[]): string {
    const mandatoryFields = diiTokens
      .filter((x) => x.isMandatory === true)
      .map((y) => y.venioFieldName)
    const nativeItem = diiTokens.find(
      (x) => x.venioFieldName === 'EXPORT_NATIVE_PATH'
    )
    let tokenInfo: string =
      'The fields <strong>' +
      mandatoryFields.join(', ') +
      '</strong> are mandatory.'
    if (nativeItem) {
      tokenInfo = tokenInfo.concat(
        '<br><strong>EDOC</strong> token is included for parent only.<br><strong>EATTACH, PARENTID</strong> tokens are included for child only.'
      )
    } else {
      tokenInfo = tokenInfo.concat(
        '<br><strong>PARENTID</strong> token is included for child only.'
      )
    }
    return tokenInfo
  }

  toggleTokenInfoTooltip(visible: boolean): void {
    this.isTokenInfoTooltipVisible = visible
  }

  toggleDiiTokenTooltip(visible: boolean): void {
    this.isDiiTokensTooltipVisible = visible
    if (this.isDIIPopoverOpen) {
      this.isDiiTokensTooltipVisible = false
    }
  }

  toggleResetDiiTokenTooltip(visible: boolean): void {
    this.isResetDIITokensInfoTooltipVisible = visible
  }

  onResetDIITokenClick(): void {
    this.selectedDIITokenKeys = this.preservedDIITokens
      .filter((x) => x.isSelected === true)
      .map((y) => y.tokenId)

    const tempDIITokens: DIITokenModel[] = cloneDeep(this.preservedDIITokens)
    this.diiTokens = tempDIITokens
    this.productionFormatFormGroup.controls.diiTokens.patchValue(tempDIITokens)
  }

  onDIITokenEditorPreparing(e: any): void {
    if (e.parentType === 'dataRow') {
      e.editorOptions.disabled = e.row.data.isMandatory
    }
  }

  onDIITokenCellClick(cell: any): void {
    if (
      cell.rowType == 'data' &&
      cell.column.dataField == 'displayName' &&
      cell.row.isSelected &&
      cell.data.tokenName === 'C'
    ) {
      cell.component.editCell(cell.rowIndex, cell.column.dataField)
    }
  }

  onDIITokenSelectionChanged(e: any): void {
    this.selectedDIITokenKeys = e.selectedRowKeys
    this.diiTokensDataGrid.instance.saveEditData()

    const tempDIITokens = cloneDeep(
      this.productionFormatFormGroup.controls.diiTokens.value
    )

    if (e.currentSelectedRowKeys) {
      e.currentSelectedRowKeys.forEach((tokenId) => {
        tempDIITokens.forEach((tempToken) => {
          if (tempToken.tokenId === tokenId) {
            tempToken.isSelected = true
          }
        })
      })
    }
    if (e.currentDeselectedRowKeys) {
      e.currentDeselectedRowKeys.forEach((tokenId) => {
        const currentDeselectedDiiTokenInfo = tempDIITokens.find(
          (x) => x.tokenId === tokenId
        )
        if (!currentDeselectedDiiTokenInfo.isMandatory) {
          tempDIITokens.forEach((tempToken) => {
            if (tempToken.tokenId == tokenId) {
              tempToken.isSelected = false
            }
          })
        } else {
          this.selectedDIITokenKeys.push(currentDeselectedDiiTokenInfo.tokenId)
        }
      })
    }

    this.productionFormatFormGroup.controls.diiTokens.patchValue(tempDIITokens)
  }

  onDiiTokenPopoverHidden(e): void {
    this.diiTokensDataGrid.instance.saveEditData()

    const tempDIITokens = cloneDeep(
      this.productionFormatFormGroup.controls.diiTokens.value
    )
    const gridDIITokens: DIITokenModel[] = cloneDeep(this.diiTokens)

    tempDIITokens.forEach((overiddenToken) => {
      gridDIITokens.forEach((gridToken) => {
        if (gridToken.tokenId === overiddenToken.tokenId) {
          overiddenToken.displayName = gridToken.displayName
        }
      })
    })

    this.productionFormatFormGroup.controls.diiTokens.patchValue(tempDIITokens)
  }
}
