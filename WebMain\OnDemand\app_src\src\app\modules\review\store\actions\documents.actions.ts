import { Dictionary } from '@ngrx/entity'
import { createAction, props } from '@ngrx/store'
import { FieldModel } from '@shared/models'
import {
  ChunkDocumentModel,
  CodingFieldSavedModel,
  CodingSaveModel,
  CodingSummary,
  DocumentCodingModel,
  DocumentEmailThread,
  DocumentMetadata,
  DocumentNotesResponseModel,
  DocumentShareModel,
  DocumentShareUserModel,
  DocumentTag,
  DocumentTagRequestModel,
  DuplicateDoc,
  EditDocumentNoteRequestModel,
  ParentChild,
  TagSavedModel,
  ViewerSettings
} from '../../models/document.model'
import {
  Export,
  Limit,
  PrintStatusModel,
  PrintToPDFParameterModel,
  SelectionParameterModel,
  ShowSummaryStatus,
  Summary,
  SummaryTiffCountParameterModel
} from '../../models/print.model'
import { ReviewSpinnerType } from '../reducers/documents.reducer'

export const setCurrentDocumentTablePage = createAction(
  '[Document] Set Current Document Table Page',
  props<{
    payload: {
      pageNumber: number
      resetSelectionItem: string
      paginatedDocumentIndex?: number
    }
  }>()
)

export const moveToNextDocumentTablePage = createAction(
  '[Document] Move To Next Document Table Page',
  props<{ payload: { resetSelectionItem: string } }>()
)

export const moveBackDocumentTablePage = createAction(
  '[Document] Move Back To Previous Document Table Page',
  props<{ payload: { resetSelectionItem: string } }>()
)

export const setCurrentDocument = createAction(
  '[Document] Set Current Document',
  props<{
    payload: {
      documentId: number
      resetSelection: boolean
      conversationId: string
    }
  }>()
)

export const setCurrentDocumentSuccess = createAction(
  '[Document] Set Current Document Success',
  props<{
    payload: {
      documentId: number
      resetSelection: boolean
      conversationId: string
    }
  }>()
)

export const setCurrentDocumentAuto = createAction(
  '[Document] Set Current Document Auto',
  props<{
    payload: {
      documentId: number
      resetSelection: boolean
    }
  }>()
)

export const moveToNextDocument = createAction(
  '[Document] Move To Next Document'
)

export const moveBackDocument = createAction(
  '[Document] Move Back Previous Document'
)

export const unsetCurrentDocument = createAction(
  '[Document] Unset Current Document'
)

export const setSelectedDocuments = createAction(
  '[Document] Set Selected Documents',
  props<{ payload: { selectedDocuments: number[] } }>()
)

export const addToSelectedDocuments = createAction(
  '[Document] Add To Selected Documents',
  props<{ payload: { documentIds: number[] } }>()
)

export const removeFromSelectedDocuments = createAction(
  '[Document] Remove From Selected Documents',
  props<{ payload: { documentIds: number[] } }>()
)

export const setUnSelectedDocuments = createAction(
  '[Document] Set Unselected Documents',
  props<{ payload: { unselectedDocuments: number[] } }>()
)

export const postDocumentSelection = createAction(
  '[Document] Post Document Selection',
  props<{ payload: { isForcedFetch: boolean } }>()
)

export const setIsBatchSelection = createAction(
  '[Document] Set Is Batch Selection',
  props<{ payload: { isBatchSelection: boolean } }>()
)

export const fetchDocumentTags = createAction(
  '[Document] Fetch Document Tags',
  props<{
    payload: {
      documentTagRequest: DocumentTagRequestModel
    }
  }>()
)

export const setDocumentTags = createAction(
  '[Document] Set Document Tags',
  props<{ payload: { documentTags: DocumentTag[] } }>()
)

/**
 * Action to add newly added tag to the store.
 */
export const addDocumentTag = createAction(
  '[Document] Add Document Tag',
  props<{ payload: { tag: DocumentTag } }>()
)

export const setTagSavedLatest = createAction(
  '[Document] Set The Latest Tag Save Changed',
  props<{ payload: { tagSavedLatest: TagSavedModel } }>()
)

export const setCodingFieldSavedLatest = createAction(
  '[Document] Set The Latest Coding Field Save Changed',
  props<{ payload: { codingFieldSavedLatest: CodingFieldSavedModel } }>()
)

// Edited: Sanh Huynh; Date: April 28, 2020; Ref #23822 - Review: Split details panel into individual panels
export const fetchDocumentMetadata = createAction(
  '[Document] Fetch Document Metadata',
  props<{
    payload: {
      fileId: number
      projectId: number
    }
  }>()
)

export const fetchDocumentParentChild = createAction(
  '[Document] Fetch Document Parent Child',
  props<{
    payload: {
      fileId: number
      projectId: number
    }
  }>()
)

export const fetchDocumentEmailThread = createAction(
  '[Document] Fetch Document Email Thread',
  props<{
    payload: {
      fileId: number
      projectId: number
    }
  }>()
)

export const fetchDocumentDuplicates = createAction(
  '[Document] Fetch Document Duplicates',
  props<{
    payload: {
      fileId: number
      projectId: number
    }
  }>()
)

export const setDocumentMetadata = createAction(
  '[Document] Set Document Metadata',
  props<{ payload: { documentMetadata: DocumentMetadata[] } }>()
)

export const setDocumentParentChild = createAction(
  '[Document] Set Document ParentChild',
  props<{ payload: { parentChild: ParentChild[] } }>()
)

export const setDocumentEmailThread = createAction(
  '[Document] Set Document Email Thread',
  props<{ payload: { emailThread: DocumentEmailThread[] } }>()
)

export const setDocumentDuplicates = createAction(
  '[Document] Set Document Duplicates',
  props<{ payload: { duplicateDocs: DuplicateDoc[] } }>()
)

export const fetchDocumentNotes = createAction(
  '[Document] Fetch Document Notes',
  props<{
    payload: {
      fileId: number
      projectId: number
      uniqueId: string
      isRefreshGrid?: boolean
    }
  }>()
)

export const setDocumentNotes = createAction(
  '[Document] Set Document Notes',
  props<{ payload: { documentNotesResponse: DocumentNotesResponseModel } }>()
)

export const addDocumentNote = createAction(
  '[Document] Add Document Note',
  props<{
    payload: {
      editDocumentNoteRequest: EditDocumentNoteRequestModel
      fileId: number
      isReply: boolean
      projectId: number
      userId: number
      isExternalUser: boolean
      docShareToken: string
    }
  }>()
)

export const editDocumentNote = createAction(
  '[Document] Edit Document Note',
  props<{
    payload: {
      editDocumentNoteRequest: EditDocumentNoteRequestModel
      projectId: number
      userId: number
      isExternalUser: boolean
      fileId: number
    }
  }>()
)

export const deleteDocumentNote = createAction(
  '[Document] Delete Document Note',
  props<{
    payload: {
      commentId: number
      projectId: number
      userId: number
      isExternalUser: boolean
      fileId: number
    }
  }>()
)

export const fetchDocumentNoteVisibleUsers = createAction(
  '[Document] Fetch Document Note Visible Users',
  props<{
    payload: { projectId: number; accessibility: string }
  }>()
)

export const setDocumentNoteVisibleUsers = createAction(
  '[Document] Set Document Note Visible Users',
  props<{ payload: { users: string[] } }>()
)

export const insertDocumentViewLog = createAction(
  '[Document] Insert Document View Log',
  props<{
    payload: {
      documentId: number
      projectId: number
      moduleName: string
    }
  }>()
)

export const fetchViewerSettings = createAction(
  '[Document] Fetch Viewer Settings',
  props<{
    payload: {
      documentId: number
      projectId: number
      userId: number
      imageSetId: number
      isExternalUser: boolean
    }
  }>()
)

export const setViewerSettings = createAction(
  '[Document] Set Viewer Settings',
  props<{ payload: { viewerSettings: ViewerSettings } }>()
)

export const fetchFullText = createAction(
  '[Document] Fetch Full Text',
  props<{ payload: { requestModel: ChunkDocumentModel } }>()
)

export const clearFulltextResponse = createAction(
  '[Document] Clear Fulltext Response'
)

export const setFullText = createAction(
  '[Document] Set Full Text',
  props<{ payload: { fullText: string } }>()
)

export const fetchDocumentHtmlText = createAction(
  '[Document] Fetch Document HTML Text',
  props<{ payload: { requestModel: ChunkDocumentModel } }>()
)

export const setDocumentHtmlText = createAction(
  '[Document] Set Document HTML Text',
  props<{ payload: { htmlText: string } }>()
)

export const setHtmlInProgressMessage = createAction(
  '[Document] Set Html In Progress Message',
  props<{ payload: { message: string } }>()
)

export const fetchDocumentSyncfusionText = createAction(
  '[Document] Fetch Document Syncfusion Text',
  props<{ payload: { requestModel: ChunkDocumentModel } }>()
)

export const setDocumentSyncfusionText = createAction(
  '[Document] Set Document Syncfusion Text',
  props<{ payload: { syncfusionText: string } }>()
)

export const saveTags = createAction(
  '[Document] Save Tags',
  props<{
    payload: { documentTags: Dictionary<DocumentTag>; navigationType: string }
  }>()
)

export const setProgressSpinner = createAction(
  '[Document] Set Progress Spinner',
  props<{ payload: { show: boolean; type: ReviewSpinnerType } }>()
)

export const showAllProgressSpinners = createAction(
  '[Document] Show All Progress Spinners'
)

export const hideAllProgressSpinners = createAction(
  '[Document] Hide All Progress Spinners'
)

export const fetchDocumentShareUsersInternal = createAction(
  '[Document] Fetch Document Share Users Internal',
  props<{ payload: { projectId: number } }>()
)

export const setDocumentShareUsersInternal = createAction(
  '[Document] Set Document Share Users Internal',
  props<{ payload: { userModels: DocumentShareUserModel[] } }>()
)

export const fetchDocumentShareUsersExternal = createAction(
  '[Document] Fetch Document Share Users External',
  props<{ payload: { projectId: number } }>()
)

export const setDocumentShareUsersExternal = createAction(
  '[Document] Set Document Share Users External',
  props<{ payload: { userModels: DocumentShareUserModel[] } }>()
)

export const shareDocuments = createAction(
  '[Document] Share Documents',
  props<{ payload: { projectId: number; requestModel: DocumentShareModel } }>()
)
export const getCodingFields = createAction(
  '[Document] Get Coding Fields',
  props<{
    payload: {
      fileId: number
      type: string
      projectId: number
    }
  }>()
)
export const setCodingFields = createAction(
  '[Document] Set Coding Fields',
  props<{ payload: { responseModel: DocumentCodingModel[] } }>()
)

export const saveCodingFields = createAction(
  '[Document] Save Coding Fields',
  props<{
    payload: {
      saveCodingModel: CodingSaveModel
      projectId: number
      navigationType?: string
    }
  }>()
)

export const saveCodingFieldsSuccess = createAction(
  '[Document] Save Coding Fields Success',
  props<{ payload: { updatedCodingModel: CodingSummary[] } }>()
)

export const setCodingSummary = createAction(
  '[Document] Set Coding Summary',
  props<{ payload: { summaryDetails: CodingSummary[] } }>()
)

export const resetCodingSummary = createAction(
  '[Document] Reset Coding Summary'
)

export const updateDocumentTable = createAction(
  '[Document] Update Document Table Grid',
  props<{ payload: { updatedCodingModel: CodingSummary[] } }>()
)

export const getTiffLimitSetting = createAction(
  '[Document] Get Tiff Limit Setting',
  props<{ payload: { projectId: number } }>()
)

export const setTiffLimitSetting = createAction(
  '[Document] Set Tiff Limit Setting',
  props<{ payload: { limitModels: Limit } }>()
)

export const getImageCount = createAction(
  '[Document] Get Image Count',
  props<{
    payload: { projectId: number; selectionParameter: SelectionParameterModel }
  }>()
)

export const setImageCount = createAction(
  '[Document] Set Image Count',
  props<{ payload: { imageCount: number } }>()
)

export const getExport = createAction(
  '[Document] Get Export',
  props<{
    payload: { projectId: number; selectionParameter: SelectionParameterModel }
  }>()
)

export const setExport = createAction(
  '[Document] Set Export',
  props<{ payload: { exportModels: Export[] } }>()
)

export const getSlipSheetField = createAction(
  '[Document] Get Slipsheet Field For Print',
  props<{ payload: { projectId: number } }>()
)

export const setSlipSheetField = createAction(
  '[Document] Set Slipsheet Field For Print',
  props<{ payload: { slipSheetModel: FieldModel[] } }>()
)

export const checkPrintName = createAction(
  '[Document] Check Print Name',
  props<{ payload: { projectId: number; printJobName: string } }>()
)

export const validatePrintName = createAction(
  '[Document] Validate Print name',
  props<{ payload: { printName: boolean } }>()
)

export const getSummary = createAction(
  '[Document] Get Summary For Print',
  props<{
    payload: {
      projectId: number
      summarySelectionParameter: SummaryTiffCountParameterModel
    }
  }>()
)

export const setSummary = createAction(
  '[Document] Set Summary For Print',
  props<{ payload: { summaryModels: Summary } }>()
)

export const showSummaryStatus = createAction(
  '[Document] Show Summary For Print',
  props<{ payload: { showSummary: ShowSummaryStatus } }>()
)

export const clearSummary = createAction(
  '[Document] Clear Summary ',
  props<{
    payload: { summaryModels: Summary; showSummary: ShowSummaryStatus }
  }>()
)

export const printDocuments = createAction(
  '[Document] Print Documents',
  props<{
    payload: {
      projectId: number
      printToPDFParameter: PrintToPDFParameterModel
    }
  }>()
)

export const getDownloadStatus = createAction(
  '[Document] Get Download Status',
  props<{ payload: { projectId: number } }>()
)

export const setDownloadStatus = createAction(
  '[Document] Set Download Status',
  props<{ payload: { downloadStatusModels: PrintStatusModel[] } }>()
)

export const deletePrintDocument = createAction(
  '[Document] Delete Print Document ',
  props<{ payload: { projectId: number; printId: number } }>()
)

export const downloadCSVReviewDocument = createAction(
  '[Document] Download CSV ReviewDocument',
  props<{
    payload: {
      pageSize: number
      pageNumber: number
      totalDocuments: number
      columnData: []
      includeWholeThreadInExport: boolean
    }
  }>()
)

export const getPdfAnnotations = createAction(
  '[Document] Get PDF Annotations',
  props<{ payload: { projectId: number; fileID: number } }>()
)

export const setPdfAnnotations = createAction(
  '[Document] Set PDF Annotations',
  props<{ payload: { xfdfString: any } }>()
)

export const getSyncfusionAnnotations = createAction(
  '[Document] Get Syncfusion PDF Annotations',
  props<{ payload: { projectId: number; fileId: number } }>()
)

export const setSyncfusionPdfAnnotations = createAction(
  '[Document] Set PDF Annotations',
  props<{ payload: { annotationJson: any } }>()
)

export const calculateDocumentNumber = createAction(
  '[Document] Selected document number',
  props<{
    payload: {
      currentDocumentId: number
    }
  }>()
)

export const moveToFirstOrLastDocumentTablePage = createAction(
  '[Document] Move To First Or Last Document Table Page',
  props<{ payload: { action: 'first' | 'last' } }>()
)

export const navigateDocumentFromNumber = createAction(
  '[Document] Navigate Document From Number Selected',
  props<{ payload: { documentNumber: number } }>()
)
