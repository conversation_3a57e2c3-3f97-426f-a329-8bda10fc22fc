import { CommaSeparatedNumberConverter } from '@shared/utils/json-converters'
import { NavigationType } from '@stores/models'
import { JsonObject, JsonProperty } from 'json2typescript'

export enum ReviewViewType {
  Search,
  EmailThread
}
@JsonObject('SearchHistoryReview')
export class SearchHistory {
  @JsonProperty('Id', Number, true) id: number = null

  @JsonProperty('IncludePC', Boolean, true) includePC: boolean = null

  @JsonProperty('NavigationBy', String, true) navigationBy: string = null

  @JsonProperty('NavigationList', CommaSeparatedNumberConverter, true)
  navigationList: number[] = null

  @JsonProperty('SearchExpression', String, true)
  searchExpression: string = null

  @JsonProperty('DynamicFolder', String, true)
  dynamicFolder: string = null

  @JsonProperty('SearchName', String, true) searchName: string = null

  @JsonProperty('SearchedBy', String, true) searchedBy: string = null

  @JsonProperty('SearchedDate', String, true) searchedDate: string = null

  @JsonProperty('TotalHits', Number, true) totalHits: number = null

  @JsonProperty('IsPrivateSearch', String, true) isPrivateSearch: string = null

  @JsonProperty('IsLoadFileSearch', Boolean, true)
  isLoadFileSearch: boolean = null

  @JsonProperty('IsSavedSearch', Boolean, true)
  isSavedSearch: boolean = null

  @JsonProperty('IsSqlMode', String, true)
  isSqlMode: string = null

  @JsonProperty('SearchDuplicateOption', Number, true)
  searchDuplicateOption: number = null

  @JsonProperty('TagGroupId', Number, true)
  tagGroupId: number = null

  @JsonProperty('CustomFieldId', Number, true)
  customFieldId: number = null

  @JsonProperty('IsDynamicFolderGlobal', Boolean, true)
  isDynamicFolderGlobal: boolean = null
}

@JsonObject('SearchResultRequestModelReview')
export class SearchResultRequestModel {
  @JsonProperty('ComputedSearchTempTable', String, true)
  computedSearchTempTable: string = null

  @JsonProperty('DSID', String, true) dsid: string = null

  @JsonProperty('Guid', String, true) guid: string = null

  @JsonProperty('IsExternalUser', Boolean, true) isExternalUser: boolean = null

  @JsonProperty('PageNumber', Number, true) pageNumber: number = null

  @JsonProperty('PageSize', String, true) pageSize: string = null

  @JsonProperty('ProjectId', String, true) projectId: string = null

  @JsonProperty('SearchResultTempTable', String, true)
  searchResultTempTable: string = null

  @JsonProperty('SessionId', String, true) sessionId: string = null

  @JsonProperty('UserId', String, true) userId: string = null

  @JsonProperty('TotalDocuments', Number, true) totalDocuments?: number = null

  @JsonProperty('ColumnData', [], true) columnData?: string[] = null

  @JsonProperty('IsBatchSelected', Boolean, true) isBatchSelected?: boolean =
    false

  @JsonProperty('SelectedFileIds', [], true) selectedFileIds?: number[] = null

  @JsonProperty('UnSelectedFileIds', [], true) unSelectedFileIds?: number[] =
    null

  @JsonProperty('viewType', Number, true) viewType?: ReviewViewType =
    ReviewViewType.Search

  @JsonProperty('IncludeWholeThreadInExport', Boolean, true)
  includeWholeThreadInExport?: boolean = false
}

// Search Result Request Models: End //

@JsonObject('SaveSearchRequestModelReview')
export class SaveSearchRequestModel {
  @JsonProperty('SaveOnCustomField', Boolean, true)
  saveOnCustomField: boolean = null

  @JsonProperty('SearchGuid', String, true) searchGuid: string = null

  @JsonProperty('SearchName', String, true) searchName: string = null

  @JsonProperty('IsNewCustomField', Boolean, true) isNewCustomField?: boolean =
    null

  @JsonProperty('CustomFieldId', Number, true) customFieldId?: number = null

  @JsonProperty('CustomFieldName', String, true) customFieldName: string = null

  @JsonProperty('ApplyAutoTagBasedOnSearchTerm', Boolean, true)
  applyAutoTagBasedOnSearchTerm?: boolean = false

  @JsonProperty('UseExistingTag', Boolean, true) useExistingTag?: boolean =
    false

  @JsonProperty('TagGroupIdOfExistingSavedSearch', Number, true)
  tagGroupIdOfExistingSavedSearch?: number = null
}

export class SearchDocumentMetadata {
  key: string

  value: any
}

export class SearchResponseData {
  fileId: number

  metadata: SearchDocumentMetadata[]
}

export class ViewSession {
  searchResultTempTable: string

  computedSearchTempTable?: string

  viewTypeRecordsTable?: string

  viewTypePagingTable?: string
}
export class SearchResultRequestData {
  pageNumber: number

  pageSize: number

  viewSession: ViewSession

  venioFieldIds?: number[]

  customFieldIds?: number[]

  tagIds?: number[]

  overrideGroupSecurity?: boolean

  searchFieldViewerType?: SearchFieldViewerType

  //reviewSetId is optional field because, it is only required for document table data.
  reviewSetId?: number

  isExternalUser?: boolean

  viewType?: ReviewViewType

  reviewLayoutPanelId?: number

  documentShareToken?: string
}

export class LoadFileSearchHistory {
  searchId: number

  searchName: string

  isSavedSearch: boolean

  isLoadFile: boolean

  isSearchFromHistory: boolean
}

export enum SearchFieldViewerType {
  List_View = 0,
  Meta_Detail = 1,
  Detail_View = 2,
  Search_Filter = 3,
  View_Similar_Docs = 4,
  Fulltext_Viewer = 5,
  Thread_View = 6,
  Field_Search = 7,
  Tiff_QC = 8
}

export interface EmailThreadViewStatusModel {
  globalTempTable: string
}

export interface SearchQueryRequest {
  searchQuery: string
  includePC: boolean
  searchDuplicateOption: number
  mediaList?: number[]
  folderList?: number[]
  dynamicFolderId?: number
  isDynamicFolderGlobal?: boolean
  navigationType?: NavigationType
  isSqlMode: boolean
  searchId: number
}

export interface SavedSearchHierarchyModel {
  id: number
  parentId: number
  name: string
  isFolder: boolean
  searchExpression: string
  isLoadFileSearch: boolean
  hasHighlightTerms: boolean
  highlightTermsColor: string
  includeParentChild?: boolean
}

export interface SearchHistoryRequestModel {
  getSavedSearchOnly: boolean
  pageNumber: number
  pageSize: number
  sortFieldName: string
  sortOrder: string
}

export interface SearchHistoryModel {
  id: number
  searchExpression: string
  searchedBy: string
  searchedDate: string
  totalHits: number
  navigationList: number[]
  navigationBy: string
  searchName: string
  includePC: boolean
  isPrivateSearch: string
  dynamicFolder: string
  isLoadFileSearch: boolean
  isSavedSearch: boolean
  hasFormState?: boolean
  hasHighlightTerms: boolean
  highlightTermsColor: string
  isSqlMode: string
  searchDuplicateOption: number
  tagGroupId: number
  customFieldId: number
  isDynamicFolderGlobal: boolean
}

export interface SearchHistoryResponseModel {
  searchHistory: Array<SearchHistoryModel>
  historyCount: number
}

export enum ActionType {
  Add,
  Remove,
  Reset
}

export interface CustomFieldAndTagGroupIdModel {
  CustomFieldId: number
  TagGroupId: number
}

export interface DelSearchHistoryRequestModel {
  selectedSearchHistory: number[]
  selectedSavedSearchId: number[]
  deleteSearchCustomField: boolean
  deleteSearchAutoTags: boolean
  deleteOtherSearchesWithSameExpression: boolean
}

export interface SearchHitReportRequestModel {
  projectId: number
  searchGuid: string
}

export interface SavedSearchWithTagModel {
  searchId: number
  searchName: string
  tagGroupId: number
  tagGroupName: string
}
